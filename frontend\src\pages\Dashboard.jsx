import React from 'react';
import { Container, Row, Col, Card } from 'react-bootstrap';
import { Helmet } from 'react-helmet-async';

const Dashboard = () => {
  const stats = [
    {
      title: 'Total Customers',
      value: '1,234',
      icon: 'bi-people',
      color: 'primary',
      change: '+12%',
      changeType: 'positive'
    },
    {
      title: 'Active Services',
      value: '89',
      icon: 'bi-tools',
      color: 'success',
      change: '+5%',
      changeType: 'positive'
    },
    {
      title: 'Monthly Revenue',
      value: '₹2,45,000',
      icon: 'bi-currency-rupee',
      color: 'info',
      change: '+18%',
      changeType: 'positive'
    },
    {
      title: 'Pending Tasks',
      value: '23',
      icon: 'bi-list-task',
      color: 'warning',
      change: '-8%',
      changeType: 'negative'
    }
  ];

  const recentActivities = [
    {
      id: 1,
      type: 'customer',
      message: 'New customer "ABC Enterprises" registered',
      time: '2 minutes ago',
      icon: 'bi-person-plus',
      color: 'success'
    },
    {
      id: 2,
      type: 'service',
      message: 'Service call completed for "XYZ Ltd"',
      time: '15 minutes ago',
      icon: 'bi-check-circle',
      color: 'primary'
    },
    {
      id: 3,
      type: 'sale',
      message: 'New sale recorded - ₹50,000',
      time: '1 hour ago',
      icon: 'bi-graph-up',
      color: 'info'
    },
    {
      id: 4,
      type: 'amc',
      message: 'AMC renewal reminder sent to 5 customers',
      time: '2 hours ago',
      icon: 'bi-bell',
      color: 'warning'
    }
  ];

  return (
    <>
      <Helmet>
        <title>Dashboard - TallyCRM</title>
      </Helmet>
      
      <Container fluid>
        {/* Page Header */}
        <Row className="mb-4">
          <Col>
            <h1 className="h3 mb-0">Dashboard</h1>
            <p className="text-muted">Welcome back! Here's what's happening with your business today.</p>
          </Col>
        </Row>

        {/* Stats Cards */}
        <Row className="mb-4">
          {stats.map((stat, index) => (
            <Col key={index} sm={6} lg={3} className="mb-3">
              <Card className="h-100 border-0 shadow-sm">
                <Card.Body>
                  <div className="d-flex align-items-center">
                    <div className={`rounded-circle bg-${stat.color} bg-opacity-10 p-3 me-3`}>
                      <i className={`bi ${stat.icon} text-${stat.color} fs-4`}></i>
                    </div>
                    <div className="flex-grow-1">
                      <h3 className="mb-0">{stat.value}</h3>
                      <p className="text-muted mb-1">{stat.title}</p>
                      <small className={`text-${stat.changeType === 'positive' ? 'success' : 'danger'}`}>
                        {stat.change} from last month
                      </small>
                    </div>
                  </div>
                </Card.Body>
              </Card>
            </Col>
          ))}
        </Row>

        <Row>
          {/* Recent Activities */}
          <Col lg={8} className="mb-4">
            <Card className="h-100 border-0 shadow-sm">
              <Card.Header className="bg-white border-bottom">
                <h5 className="mb-0">Recent Activities</h5>
              </Card.Header>
              <Card.Body>
                <div className="list-group list-group-flush">
                  {recentActivities.map((activity) => (
                    <div key={activity.id} className="list-group-item border-0 px-0">
                      <div className="d-flex align-items-center">
                        <div className={`rounded-circle bg-${activity.color} bg-opacity-10 p-2 me-3`}>
                          <i className={`bi ${activity.icon} text-${activity.color}`}></i>
                        </div>
                        <div className="flex-grow-1">
                          <p className="mb-1">{activity.message}</p>
                          <small className="text-muted">{activity.time}</small>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </Card.Body>
            </Card>
          </Col>

          {/* Quick Actions */}
          <Col lg={4} className="mb-4">
            <Card className="h-100 border-0 shadow-sm">
              <Card.Header className="bg-white border-bottom">
                <h5 className="mb-0">Quick Actions</h5>
              </Card.Header>
              <Card.Body>
                <div className="d-grid gap-2">
                  <button className="btn btn-primary">
                    <i className="bi bi-person-plus me-2"></i>
                    Add New Customer
                  </button>
                  <button className="btn btn-outline-primary">
                    <i className="bi bi-tools me-2"></i>
                    Create Service Call
                  </button>
                  <button className="btn btn-outline-primary">
                    <i className="bi bi-graph-up me-2"></i>
                    Record Sale
                  </button>
                  <button className="btn btn-outline-primary">
                    <i className="bi bi-file-earmark-text me-2"></i>
                    Generate Report
                  </button>
                </div>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      </Container>
    </>
  );
};

export default Dashboard;
