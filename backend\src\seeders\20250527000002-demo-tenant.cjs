'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Create demo tenant
    const tenantId = '550e8400-e29b-41d4-a716-446655440100';
    await queryInterface.bulkInsert('tenants', [{
      id: tenantId,
      name: 'Demo Company',
      subdomain: 'demo',
      status: 'active',
      plan: 'pro',
      max_users: 50,
      settings: JSON.stringify({
        timezone: 'Asia/Kolkata',
        currency: 'INR',
        date_format: 'DD/MM/YYYY',
        theme: 'light'
      }),
      created_at: new Date(),
      updated_at: new Date(),
    }]);

    // Create roles for the demo tenant
    const roles = [
      {
        id: '550e8400-e29b-41d4-a716-446655440200',
        tenant_id: tenantId,
        name: 'Super Admin',
        description: 'Full system access',
        is_system_role: true,
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440201',
        tenant_id: tenantId,
        name: 'Admin',
        description: 'Administrative access',
        is_system_role: false,
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440202',
        tenant_id: tenantId,
        name: 'Manager',
        description: 'Management level access',
        is_system_role: false,
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440203',
        tenant_id: tenantId,
        name: 'Sales Executive',
        description: 'Sales operations access',
        is_system_role: false,
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440204',
        tenant_id: tenantId,
        name: 'Service Engineer',
        description: 'Service operations access',
        is_system_role: false,
        created_at: new Date(),
        updated_at: new Date(),
      },
    ];

    await queryInterface.bulkInsert('roles', roles);

    // Assign permissions to roles
    const rolePermissions = [
      // Super Admin - All permissions
      ...['550e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440003', 
          '550e8400-e29b-41d4-a716-446655440004', '550e8400-e29b-41d4-a716-446655440005', '550e8400-e29b-41d4-a716-446655440010',
          '550e8400-e29b-41d4-a716-446655440011', '550e8400-e29b-41d4-a716-446655440012', '550e8400-e29b-41d4-a716-446655440013',
          '550e8400-e29b-41d4-a716-446655440014', '550e8400-e29b-41d4-a716-446655440020', '550e8400-e29b-41d4-a716-446655440021',
          '550e8400-e29b-41d4-a716-446655440022', '550e8400-e29b-41d4-a716-446655440023', '550e8400-e29b-41d4-a716-446655440030',
          '550e8400-e29b-41d4-a716-446655440031', '550e8400-e29b-41d4-a716-446655440032', '550e8400-e29b-41d4-a716-446655440033',
          '550e8400-e29b-41d4-a716-446655440040', '550e8400-e29b-41d4-a716-446655440041', '550e8400-e29b-41d4-a716-446655440042',
          '550e8400-e29b-41d4-a716-446655440043', '550e8400-e29b-41d4-a716-446655440050', '550e8400-e29b-41d4-a716-446655440051',
          '550e8400-e29b-41d4-a716-446655440060', '550e8400-e29b-41d4-a716-446655440061'].map(permissionId => ({
            role_id: '550e8400-e29b-41d4-a716-446655440200',
            permission_id: permissionId,
            created_at: new Date(),
            updated_at: new Date(),
          })),

      // Admin - Most permissions except system admin
      ...['550e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440003',
          '550e8400-e29b-41d4-a716-446655440004', '550e8400-e29b-41d4-a716-446655440005', '550e8400-e29b-41d4-a716-446655440011',
          '550e8400-e29b-41d4-a716-446655440020', '550e8400-e29b-41d4-a716-446655440021', '550e8400-e29b-41d4-a716-446655440022',
          '550e8400-e29b-41d4-a716-446655440023', '550e8400-e29b-41d4-a716-446655440030', '550e8400-e29b-41d4-a716-446655440031',
          '550e8400-e29b-41d4-a716-446655440032', '550e8400-e29b-41d4-a716-446655440033', '550e8400-e29b-41d4-a716-446655440040',
          '550e8400-e29b-41d4-a716-446655440041', '550e8400-e29b-41d4-a716-446655440042', '550e8400-e29b-41d4-a716-446655440043',
          '550e8400-e29b-41d4-a716-446655440050', '550e8400-e29b-41d4-a716-446655440051'].map(permissionId => ({
            role_id: '550e8400-e29b-41d4-a716-446655440201',
            permission_id: permissionId,
            created_at: new Date(),
            updated_at: new Date(),
          })),

      // Manager - Customer, Service, Sales, Reports
      ...['550e8400-e29b-41d4-a716-446655440020', '550e8400-e29b-41d4-a716-446655440021', '550e8400-e29b-41d4-a716-446655440022',
          '550e8400-e29b-41d4-a716-446655440030', '550e8400-e29b-41d4-a716-446655440031', '550e8400-e29b-41d4-a716-446655440032',
          '550e8400-e29b-41d4-a716-446655440040', '550e8400-e29b-41d4-a716-446655440041', '550e8400-e29b-41d4-a716-446655440042',
          '550e8400-e29b-41d4-a716-446655440050', '550e8400-e29b-41d4-a716-446655440051'].map(permissionId => ({
            role_id: '550e8400-e29b-41d4-a716-446655440202',
            permission_id: permissionId,
            created_at: new Date(),
            updated_at: new Date(),
          })),

      // Sales Executive - Customer and Sales
      ...['550e8400-e29b-41d4-a716-446655440020', '550e8400-e29b-41d4-a716-446655440021', '550e8400-e29b-41d4-a716-446655440022',
          '550e8400-e29b-41d4-a716-446655440040', '550e8400-e29b-41d4-a716-446655440041', '550e8400-e29b-41d4-a716-446655440042',
          '550e8400-e29b-41d4-a716-446655440050'].map(permissionId => ({
            role_id: '550e8400-e29b-41d4-a716-446655440203',
            permission_id: permissionId,
            created_at: new Date(),
            updated_at: new Date(),
          })),

      // Service Engineer - Customer and Service
      ...['550e8400-e29b-41d4-a716-446655440021', '550e8400-e29b-41d4-a716-446655440030', '550e8400-e29b-41d4-a716-446655440031',
          '550e8400-e29b-41d4-a716-446655440032', '550e8400-e29b-41d4-a716-446655440050'].map(permissionId => ({
            role_id: '550e8400-e29b-41d4-a716-446655440204',
            permission_id: permissionId,
            created_at: new Date(),
            updated_at: new Date(),
          })),
    ];

    await queryInterface.bulkInsert('role_permissions', rolePermissions);
  },

  async down(queryInterface, Sequelize) {
    // Remove role permissions
    await queryInterface.bulkDelete('role_permissions', {
      role_id: {
        [Sequelize.Op.in]: [
          '550e8400-e29b-41d4-a716-446655440200',
          '550e8400-e29b-41d4-a716-446655440201',
          '550e8400-e29b-41d4-a716-446655440202',
          '550e8400-e29b-41d4-a716-446655440203',
          '550e8400-e29b-41d4-a716-446655440204',
        ]
      }
    });

    // Remove roles
    await queryInterface.bulkDelete('roles', {
      tenant_id: '550e8400-e29b-41d4-a716-446655440100'
    });

    // Remove tenant
    await queryInterface.bulkDelete('tenants', {
      id: '550e8400-e29b-41d4-a716-446655440100'
    });
  }
};