'use strict';

const { v4: uuidv4 } = require('uuid');
const bcrypt = require('bcryptjs');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Get tenant and role IDs from previous seeder
    const tenantData = await queryInterface.sequelize.query(
      'SELECT value FROM temp_seeder_data WHERE key = \'demo_tenant_id\'',
      { type: queryInterface.sequelize.QueryTypes.SELECT }
    );
    
    const adminRoleData = await queryInterface.sequelize.query(
      'SELECT value FROM temp_seeder_data WHERE key = \'admin_role_id\'',
      { type: queryInterface.sequelize.QueryTypes.SELECT }
    );

    const userRoleData = await queryInterface.sequelize.query(
      'SELECT value FROM temp_seeder_data WHERE key = \'user_role_id\'',
      { type: queryInterface.sequelize.QueryTypes.SELECT }
    );

    if (tenantData.length === 0 || adminRoleData.length === 0) {
      throw new Error('Demo tenant or roles not found. Please run previous seeders first.');
    }

    const tenantId = tenantData[0].value;
    const adminRoleId = adminRoleData[0].value;
    const userRoleId = userRoleData[0].value;

    // Create demo users
    const adminUserId = uuidv4();
    const regularUserId = uuidv4();

    // Hash passwords
    const hashedAdminPassword = await bcrypt.hash('Admin@123', 12);
    const hashedUserPassword = await bcrypt.hash('User@123', 12);

    await queryInterface.bulkInsert('users', [
      {
        id: adminUserId,
        tenant_id: tenantId,
        email: '<EMAIL>',
        password: hashedAdminPassword,
        first_name: 'System',
        last_name: 'Administrator',
        phone: '+91-9876543210',
        is_active: true,
        is_verified: true,
        preferences: JSON.stringify({
          theme: 'light',
          language: 'en',
          timezone: 'Asia/Kolkata',
          notifications: {
            email: true,
            desktop: true
          }
        }),
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        id: regularUserId,
        tenant_id: tenantId,
        email: '<EMAIL>',
        password: hashedUserPassword,
        first_name: 'Demo',
        last_name: 'User',
        phone: '+91-9876543211',
        is_active: true,
        is_verified: true,
        preferences: JSON.stringify({
          theme: 'light',
          language: 'en',
          timezone: 'Asia/Kolkata'
        }),
        created_at: new Date(),
        updated_at: new Date(),
      },
    ]);

    // Assign roles to users
    await queryInterface.bulkInsert('user_roles', [
      {
        id: uuidv4(),
        user_id: adminUserId,
        role_id: adminRoleId,
        assigned_at: new Date(),
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        id: uuidv4(),
        user_id: regularUserId,
        role_id: userRoleId,
        assigned_at: new Date(),
        created_at: new Date(),
        updated_at: new Date(),
      },
    ]);
  },

  async down(queryInterface, Sequelize) {
    // Get tenant ID
    const tenantData = await queryInterface.sequelize.query(
      'SELECT value FROM temp_seeder_data WHERE key = \'demo_tenant_id\'',
      { type: queryInterface.sequelize.QueryTypes.SELECT }
    );

    if (tenantData.length > 0) {
      const tenantId = tenantData[0].value;
      await queryInterface.bulkDelete('users', { tenant_id: tenantId });
    }
  }
};
