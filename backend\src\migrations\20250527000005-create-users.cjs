'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('users', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false,
      },
      tenant_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'tenants',
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
      },
      email: {
        type: Sequelize.STRING,
        allowNull: false,
        validate: {
          isEmail: true,
        },
      },
      password: {
        type: Sequelize.STRING,
        allowNull: false,
        validate: {
          len: [8, 255],
        },
      },
      first_name: {
        type: Sequelize.STRING,
        allowNull: false,
        validate: {
          len: [2, 50],
        },
      },
      last_name: {
        type: Sequelize.STRING,
        allowNull: false,
        validate: {
          len: [2, 50],
        },
      },
      phone: {
        type: Sequelize.STRING,
        allowNull: true,
        validate: {
          len: [10, 15],
        },
      },
      is_active: {
        type: Sequelize.BOOLEAN,
        defaultValue: true,
      },
      is_verified: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      last_login_at: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      password_reset_token: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      password_reset_expires: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      email_verification_token: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      email_verification_expires: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      profile_image: {
        type: Sequelize.STRING,
        allowNull: true,
      },      preferences: {
        type: Sequelize.TEXT,
        defaultValue: '{}',
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
    });

    // Add indexes
    await queryInterface.addIndex('users', ['tenant_id'], {
      name: 'users_tenant_id_idx',
    });
    
    await queryInterface.addIndex('users', ['email'], {
      name: 'users_email_idx',
      unique: true,
    });
    
    await queryInterface.addIndex('users', ['tenant_id', 'email'], {
      name: 'users_tenant_email_idx',
      unique: true,
    });
    
    await queryInterface.addIndex('users', ['is_active'], {
      name: 'users_is_active_idx',
    });
    
    await queryInterface.addIndex('users', ['is_verified'], {
      name: 'users_is_verified_idx',
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('users');
  }
};
