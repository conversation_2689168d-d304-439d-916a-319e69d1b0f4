import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import cookieParser from 'cookie-parser';
import rateLimit from 'express-rate-limit';
import dotenv from 'dotenv';

// Import configurations
import appConfig from '../config/app.js';
import { logger } from './utils/logger.js';
import { errorHandler, notFoundHandler } from './middleware/errorHandler.js';
import { requestLogger } from './middleware/requestLogger.js';

// Import routes
import healthRoutes from './routes/health.js';
import authRoutes from './routes/auth.js';
import userRoutes from './routes/users.js';
import customerRoutes from './routes/customers.js';
import serviceCallRoutes from './routes/serviceCalls.js';

// Load environment variables
dotenv.config();

// Create Express application
const app = express();

// Trust proxy for accurate IP addresses
app.set('trust proxy', 1);

// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com", "https://cdn.jsdelivr.net"],
      fontSrc: ["'self'", "https://fonts.gstatic.com", "https://cdn.jsdelivr.net"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'"],
    },
  },
  crossOriginEmbedderPolicy: false,
}));

// CORS configuration
app.use(cors({
  origin: appConfig.frontend.corsOrigins,
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
}));

// Rate limiting
const limiter = rateLimit({
  windowMs: appConfig.security.rateLimitWindowMs,
  max: appConfig.security.rateLimitMaxRequests,
  message: {
    error: 'Too many requests from this IP, please try again later.',
  },
  standardHeaders: true,
  legacyHeaders: false,
});
app.use(limiter);

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));
app.use(cookieParser());

// Compression middleware
app.use(compression());

// Request logging middleware
app.use(requestLogger);

// API routes
app.use('/health', healthRoutes);
app.use(`${appConfig.api.prefix}/auth`, authRoutes);
app.use(`${appConfig.api.prefix}/users`, userRoutes);
app.use(`${appConfig.api.prefix}/customers`, customerRoutes);
app.use(`${appConfig.api.prefix}/service-calls`, serviceCallRoutes);

// Serve static files (uploads, etc.)
app.use('/uploads', express.static('uploads'));

// API documentation (Swagger) - only in development
if (appConfig.development.enableSwagger && appConfig.app.env === 'development') {
  try {
    const swaggerUi = await import('swagger-ui-express');
    const swaggerJsdoc = await import('swagger-jsdoc');

    const swaggerOptions = {
      definition: {
        openapi: '3.0.0',
        info: {
          title: 'TallyCRM API',
          version: appConfig.app.version,
          description: 'API documentation for TallyCRM - CRM for Tally Resellers',
        },
        servers: [
          {
            url: appConfig.app.url,
            description: 'Development server',
          },
        ],
        components: {
          securitySchemes: {
            bearerAuth: {
              type: 'http',
              scheme: 'bearer',
              bearerFormat: 'JWT',
            },
          },
        },
        security: [
          {
            bearerAuth: [],
          },
        ],
      },
      apis: ['./src/routes/*.js', './src/models/*.js'],
    };

    const specs = swaggerJsdoc.default(swaggerOptions);
    app.use(appConfig.development.swaggerPath, swaggerUi.default.serve, swaggerUi.default.setup(specs));

    logger.info(`📚 API Documentation available at ${appConfig.app.url}${appConfig.development.swaggerPath}`);
  } catch (error) {
    logger.warn('Failed to load Swagger documentation:', error.message);
  }
}

// 404 handler
app.use(notFoundHandler);

// Global error handler
app.use(errorHandler);

// Graceful shutdown
const gracefulShutdown = (signal) => {
  logger.info(`Received ${signal}. Starting graceful shutdown...`);

  server.close(() => {
    logger.info('HTTP server closed.');

    // Close database connections, cleanup resources, etc.
    process.exit(0);
  });

  // Force close after 30 seconds
  setTimeout(() => {
    logger.error('Could not close connections in time, forcefully shutting down');
    process.exit(1);
  }, 30000);
};

// Handle shutdown signals
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception:', error);
  process.exit(1);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Start server
const PORT = appConfig.app.port;
const server = app.listen(PORT, () => {
  logger.info(`🚀 TallyCRM Backend Server started successfully!`);
  logger.info(`📍 Environment: ${appConfig.app.env}`);
  logger.info(`🌐 Server running on: ${appConfig.app.url}`);
  logger.info(`📡 API Base URL: ${appConfig.app.url}${appConfig.api.prefix}`);

  if (appConfig.development.enableSwagger && appConfig.app.env === 'development') {
    logger.info(`📚 API Docs: ${appConfig.app.url}${appConfig.development.swaggerPath}`);
  }
});

export default app;
