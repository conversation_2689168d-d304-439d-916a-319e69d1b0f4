'use strict';

const { v4: uuidv4 } = require('uuid');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const permissions = [
      // User Management
      { name: 'users.create', description: 'Create new users', resource: 'users', action: 'create' },
      { name: 'users.read', description: 'View user information', resource: 'users', action: 'read' },
      { name: 'users.update', description: 'Update user information', resource: 'users', action: 'update' },
      { name: 'users.delete', description: 'Delete users', resource: 'users', action: 'delete' },
      
      // Customer Management
      { name: 'customers.create', description: 'Create new customers', resource: 'customers', action: 'create' },
      { name: 'customers.read', description: 'View customer information', resource: 'customers', action: 'read' },
      { name: 'customers.update', description: 'Update customer information', resource: 'customers', action: 'update' },
      { name: 'customers.delete', description: 'Delete customers', resource: 'customers', action: 'delete' },
      
      // Sales Management
      { name: 'sales.create', description: 'Create new sales records', resource: 'sales', action: 'create' },
      { name: 'sales.read', description: 'View sales information', resource: 'sales', action: 'read' },
      { name: 'sales.update', description: 'Update sales records', resource: 'sales', action: 'update' },
      { name: 'sales.delete', description: 'Delete sales records', resource: 'sales', action: 'delete' },
      
      // Service Management
      { name: 'services.create', description: 'Create service calls', resource: 'services', action: 'create' },
      { name: 'services.read', description: 'View service calls', resource: 'services', action: 'read' },
      { name: 'services.update', description: 'Update service calls', resource: 'services', action: 'update' },
      { name: 'services.delete', description: 'Delete service calls', resource: 'services', action: 'delete' },
      
      // Reports and Analytics
      { name: 'reports.read', description: 'View reports and analytics', resource: 'reports', action: 'read' },
      { name: 'reports.export', description: 'Export reports', resource: 'reports', action: 'export' },
      
      // Master Data Management
      { name: 'masters.create', description: 'Create master data', resource: 'masters', action: 'create' },
      { name: 'masters.read', description: 'View master data', resource: 'masters', action: 'read' },
      { name: 'masters.update', description: 'Update master data', resource: 'masters', action: 'update' },
      { name: 'masters.delete', description: 'Delete master data', resource: 'masters', action: 'delete' },
      
      // Role Management
      { name: 'roles.create', description: 'Create new roles', resource: 'roles', action: 'create' },
      { name: 'roles.read', description: 'View roles', resource: 'roles', action: 'read' },
      { name: 'roles.update', description: 'Update roles', resource: 'roles', action: 'update' },
      { name: 'roles.delete', description: 'Delete roles', resource: 'roles', action: 'delete' },
      
      // System Administration
      { name: 'admin.settings', description: 'Manage system settings', resource: 'admin', action: 'settings' },
      { name: 'admin.logs', description: 'View system logs', resource: 'admin', action: 'logs' },
      { name: 'admin.backups', description: 'Manage system backups', resource: 'admin', action: 'backups' },
    ];

    const permissionData = permissions.map(permission => ({
      id: uuidv4(),
      name: permission.name,
      description: permission.description,
      resource: permission.resource,
      action: permission.action,
      scope: 'tenant',
      is_system: true,
      created_at: new Date(),
      updated_at: new Date(),
    }));

    await queryInterface.bulkInsert('permissions', permissionData);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete('permissions', null, {});
  }
};
