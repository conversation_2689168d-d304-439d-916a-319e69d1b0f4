/* TallyCRM Global Styles */

/* CSS Variables */
:root {
  /* Primary Colors */
  --primary-color: #0d6efd;
  --primary-dark: #0b5ed7;
  --primary-light: #6ea8fe;
  
  /* Secondary Colors */
  --secondary-color: #6c757d;
  --success-color: #198754;
  --danger-color: #dc3545;
  --warning-color: #ffc107;
  --info-color: #0dcaf0;
  
  /* Neutral Colors */
  --white: #ffffff;
  --light: #f8f9fa;
  --dark: #212529;
  --muted: #6c757d;
  
  /* Background Colors */
  --bg-primary: #f8f9fa;
  --bg-secondary: #e9ecef;
  --bg-dark: #343a40;
  
  /* Border Colors */
  --border-color: #dee2e6;
  --border-light: #e9ecef;
  
  /* Shadow */
  --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  --shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
  
  /* Border Radius */
  --border-radius: 0.375rem;
  --border-radius-sm: 0.25rem;
  --border-radius-lg: 0.5rem;
  
  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 3rem;
  
  /* Typography */
  --font-family-sans-serif: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  --font-size-base: 1rem;
  --font-size-sm: 0.875rem;
  --font-size-lg: 1.125rem;
  --line-height-base: 1.5;
  
  /* Transitions */
  --transition: all 0.15s ease-in-out;
}

/* Reset and Base Styles */
* {
  box-sizing: border-box;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  margin: 0;
  font-family: var(--font-family-sans-serif);
  font-size: var(--font-size-base);
  font-weight: 400;
  line-height: var(--line-height-base);
  color: var(--dark);
  background-color: var(--bg-primary);
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  font-weight: 600;
  line-height: 1.2;
}

p {
  margin-top: 0;
  margin-bottom: 1rem;
}

/* Links */
a {
  color: var(--primary-color);
  text-decoration: none;
  transition: var(--transition);
}

a:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}

/* Buttons */
.btn {
  transition: var(--transition);
}

.btn:focus {
  box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* Cards */
.card {
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-sm);
  transition: var(--transition);
}

.card:hover {
  box-shadow: var(--shadow);
}

/* Forms */
.form-control {
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  transition: var(--transition);
}

.form-control:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* Tables */
.table {
  --bs-table-bg: transparent;
}

.table-hover tbody tr:hover {
  background-color: var(--bg-secondary);
}

/* Utilities */
.text-muted {
  color: var(--muted) !important;
}

.bg-light {
  background-color: var(--bg-primary) !important;
}

.border-light {
  border-color: var(--border-light) !important;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--muted);
  border-radius: var(--border-radius);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--secondary-color);
}

/* Loading Spinner */
.spinner-border {
  width: 2rem;
  height: 2rem;
}

.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  html {
    font-size: 14px;
  }
  
  .container-fluid {
    padding-left: 1rem;
    padding-right: 1rem;
  }
}

/* Print Styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  .card {
    border: 1px solid var(--border-color) !important;
    box-shadow: none !important;
  }
}
