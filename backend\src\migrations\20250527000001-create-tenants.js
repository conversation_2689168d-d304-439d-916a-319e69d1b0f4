'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('tenants', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false,
      },
      name: {
        type: Sequelize.STRING,
        allowNull: false,
        validate: {
          len: [2, 100],
        },
      },
      subdomain: {
        type: Sequelize.STRING,
        allowNull: false,
        unique: true,
        validate: {
          len: [3, 50],
          is: /^[a-z0-9-]+$/i,
        },
      },
      domain: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      logo: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      contact_email: {
        type: Sequelize.STRING,
        allowNull: true,
        validate: {
          isEmail: true,
        },
      },
      contact_phone: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      address: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      city: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      state: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      country: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      postal_code: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      timezone: {
        type: Sequelize.STRING,
        defaultValue: 'UTC',
      },
      currency: {
        type: Sequelize.STRING,
        defaultValue: 'INR',
      },
      subscription_plan: {
        type: Sequelize.ENUM('basic', 'standard', 'premium', 'enterprise'),
        defaultValue: 'basic',
      },
      subscription_status: {
        type: Sequelize.ENUM('active', 'inactive', 'suspended', 'cancelled'),
        defaultValue: 'active',
      },
      subscription_expires_at: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      max_users: {
        type: Sequelize.INTEGER,
        defaultValue: 5,
      },      settings: {
        type: Sequelize.TEXT,
        defaultValue: '{}',
      },
      is_active: {
        type: Sequelize.BOOLEAN,
        defaultValue: true,
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
    });

    // Add indexes
    await queryInterface.addIndex('tenants', ['subdomain'], {
      name: 'tenants_subdomain_idx',
      unique: true,
    });
    
    await queryInterface.addIndex('tenants', ['subscription_status'], {
      name: 'tenants_subscription_status_idx',
    });
    
    await queryInterface.addIndex('tenants', ['is_active'], {
      name: 'tenants_is_active_idx',
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('tenants');
  }
};
