import { Sequelize } from 'sequelize';
import { logger } from './logger.js';
import databaseConfig from '../../config/database.js';

// Get environment
const env = process.env.NODE_ENV || 'development';
const config = databaseConfig[env];

// Create Sequelize instance
const sequelize = new Sequelize(
  config.database,
  config.username,
  config.password,
  {
    host: config.host,
    port: config.port,
    dialect: config.dialect,
    logging: config.logging ? (msg) => logger.debug(msg) : false,
    pool: config.pool,
    define: config.define,
    dialectOptions: config.dialectOptions,
    
    // Additional options
    retry: {
      max: 3,
      timeout: 60000,
      match: [
        /ETIMEDOUT/,
        /EHOSTUNREACH/,
        /ECONNRESET/,
        /ECONNREFUSED/,
        /ENOTFOUND/,
        /SequelizeConnectionError/,
        /SequelizeConnectionRefusedError/,
        /SequelizeHostNotFoundError/,
        /SequelizeHostNotReachableError/,
        /SequelizeInvalidConnectionError/,
        /SequelizeConnectionTimedOutError/,
      ],
    },
    
    // Hooks
    hooks: {
      beforeConnect: () => {
        logger.debug('Attempting to connect to database...');
      },
      afterConnect: () => {
        logger.debug('Database connection established successfully');
      },
      beforeDisconnect: () => {
        logger.debug('Disconnecting from database...');
      },
      afterDisconnect: () => {
        logger.debug('Database connection closed');
      },
    },
  }
);

// Test database connection
export const testConnection = async () => {
  try {
    await sequelize.authenticate();
    logger.info('✅ Database connection has been established successfully');
    return true;
  } catch (error) {
    logger.error('❌ Unable to connect to the database:', error);
    return false;
  }
};

// Initialize database
export const initializeDatabase = async () => {
  try {
    // Test connection first
    const isConnected = await testConnection();
    if (!isConnected) {
      throw new Error('Database connection failed');
    }

    // Sync models (only in development)
    if (env === 'development') {
      await sequelize.sync({ alter: true });
      logger.info('✅ Database models synchronized');
    }

    return true;
  } catch (error) {
    logger.error('❌ Database initialization failed:', error);
    throw error;
  }
};

// Close database connection
export const closeConnection = async () => {
  try {
    await sequelize.close();
    logger.info('✅ Database connection closed successfully');
  } catch (error) {
    logger.error('❌ Error closing database connection:', error);
    throw error;
  }
};

// Get database health status
export const getDatabaseHealth = async () => {
  try {
    const startTime = Date.now();
    await sequelize.authenticate();
    const responseTime = Date.now() - startTime;

    // Get connection pool status
    const pool = sequelize.connectionManager.pool;
    const poolStatus = {
      size: pool.size,
      available: pool.available,
      using: pool.using,
      waiting: pool.waiting,
    };

    return {
      status: 'healthy',
      responseTime,
      pool: poolStatus,
      database: config.database,
      host: config.host,
      port: config.port,
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      error: error.message,
      database: config.database,
      host: config.host,
      port: config.port,
    };
  }
};

// Execute raw SQL query
export const executeQuery = async (query, replacements = {}) => {
  try {
    const [results, metadata] = await sequelize.query(query, {
      replacements,
      type: Sequelize.QueryTypes.SELECT,
    });
    return results;
  } catch (error) {
    logger.error('Query execution failed:', error);
    throw error;
  }
};

// Get database statistics
export const getDatabaseStats = async () => {
  try {
    const stats = await executeQuery(`
      SELECT 
        schemaname,
        tablename,
        n_tup_ins as inserts,
        n_tup_upd as updates,
        n_tup_del as deletes,
        n_live_tup as live_tuples,
        n_dead_tup as dead_tuples,
        pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as total_size
      FROM pg_stat_user_tables 
      ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC
      LIMIT 20;
    `);

    return stats;
  } catch (error) {
    logger.error('Failed to get database statistics:', error);
    return [];
  }
};

// Transaction wrapper
export const withTransaction = async (callback) => {
  const transaction = await sequelize.transaction();
  
  try {
    const result = await callback(transaction);
    await transaction.commit();
    return result;
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
};

// Bulk operations helper
export const bulkCreate = async (model, data, options = {}) => {
  try {
    const defaultOptions = {
      validate: true,
      ignoreDuplicates: false,
      updateOnDuplicate: false,
    };
    
    const mergedOptions = { ...defaultOptions, ...options };
    const result = await model.bulkCreate(data, mergedOptions);
    
    logger.info(`Bulk created ${result.length} records in ${model.name}`);
    return result;
  } catch (error) {
    logger.error(`Bulk create failed for ${model.name}:`, error);
    throw error;
  }
};

// Database backup helper (for development)
export const createBackup = async (backupPath) => {
  if (env === 'production') {
    throw new Error('Backup should not be created from application in production');
  }

  try {
    const { exec } = await import('child_process');
    const { promisify } = await import('util');
    const execAsync = promisify(exec);

    const command = `pg_dump -h ${config.host} -p ${config.port} -U ${config.username} -d ${config.database} -f ${backupPath}`;
    
    await execAsync(command);
    logger.info(`Database backup created: ${backupPath}`);
    return true;
  } catch (error) {
    logger.error('Backup creation failed:', error);
    throw error;
  }
};

// Export sequelize instance and utilities
export { sequelize };
export default sequelize;
