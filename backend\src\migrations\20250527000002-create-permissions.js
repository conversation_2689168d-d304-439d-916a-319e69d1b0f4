'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('permissions', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false,
      },
      name: {
        type: Sequelize.STRING,
        allowNull: false,
        unique: true,
        validate: {
          len: [2, 50],
        },
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      resource: {
        type: Sequelize.STRING,
        allowNull: false,
        comment: 'The resource this permission applies to (e.g., users, customers, sales)',
      },
      action: {
        type: Sequelize.STRING,
        allowNull: false,
        comment: 'The action allowed (e.g., create, read, update, delete)',
      },
      scope: {
        type: Sequelize.ENUM('tenant', 'own', 'assigned'),
        defaultValue: 'tenant',
        comment: 'Scope of the permission: tenant-wide, own records only, or assigned records',
      },
      is_system: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
        comment: 'System permissions cannot be deleted',
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
    });

    // Add indexes
    await queryInterface.addIndex('permissions', ['name'], {
      name: 'permissions_name_idx',
      unique: true,
    });
    
    await queryInterface.addIndex('permissions', ['resource'], {
      name: 'permissions_resource_idx',
    });
    
    await queryInterface.addIndex('permissions', ['resource', 'action'], {
      name: 'permissions_resource_action_idx',
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('permissions');
  }
};
