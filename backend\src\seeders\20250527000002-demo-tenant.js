'use strict';

const { v4: uuidv4 } = require('uuid');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const tenantId = uuidv4();
    
    // Create demo tenant
    await queryInterface.bulkInsert('tenants', [{
      id: tenantId,
      name: 'Demo Tally Reseller',
      subdomain: 'demo',
      contact_email: '<EMAIL>',
      contact_phone: '+91-9876543210',
      address: '123 Business Center, Tech Park',
      city: 'Mumbai',
      state: 'Maharashtra',
      country: 'India',
      postal_code: '400001',
      timezone: 'Asia/Kolkata',
      currency: 'INR',
      subscription_plan: 'premium',
      subscription_status: 'active',
      max_users: 50,
      settings: JSON.stringify({
        theme: 'light',
        language: 'en',
        dateFormat: 'DD/MM/YYYY',
        timeFormat: '24h',
        notifications: {
          email: true,
          sms: false,
          push: true
        }
      }),
      is_active: true,
      created_at: new Date(),
      updated_at: new Date(),
    }]);

    // Create default roles for the demo tenant
    const adminRoleId = uuidv4();
    const managerRoleId = uuidv4();
    const userRoleId = uuidv4();

    await queryInterface.bulkInsert('roles', [
      {
        id: adminRoleId,
        tenant_id: tenantId,
        name: 'Administrator',
        description: 'Full system access with all permissions',
        is_system: true,
        is_active: true,
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        id: managerRoleId,
        tenant_id: tenantId,
        name: 'Manager',
        description: 'Management level access with limited admin permissions',
        is_system: true,
        is_active: true,
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        id: userRoleId,
        tenant_id: tenantId,
        name: 'User',
        description: 'Standard user access for daily operations',
        is_system: true,
        is_active: true,
        created_at: new Date(),
        updated_at: new Date(),
      },
    ]);

    // Get all permissions
    const permissions = await queryInterface.sequelize.query(
      'SELECT id FROM permissions',
      { type: queryInterface.sequelize.QueryTypes.SELECT }
    );

    // Assign all permissions to admin role
    const adminRolePermissions = permissions.map(permission => ({
      id: uuidv4(),
      role_id: adminRoleId,
      permission_id: permission.id,
      created_at: new Date(),
      updated_at: new Date(),
    }));

    await queryInterface.bulkInsert('role_permissions', adminRolePermissions);

    // Store tenant and role IDs for use in next seeder
    await queryInterface.sequelize.query(
      `INSERT INTO "temp_seeder_data" ("key", "value") VALUES 
       ('demo_tenant_id', '${tenantId}'),
       ('admin_role_id', '${adminRoleId}'),
       ('manager_role_id', '${managerRoleId}'),
       ('user_role_id', '${userRoleId}')
       ON CONFLICT ("key") DO UPDATE SET "value" = EXCLUDED."value"`
    ).catch(() => {
      // Table might not exist, create it
      return queryInterface.sequelize.query(
        `CREATE TABLE IF NOT EXISTS "temp_seeder_data" (
          "key" VARCHAR(255) PRIMARY KEY,
          "value" VARCHAR(255) NOT NULL
        )`
      ).then(() => {
        return queryInterface.sequelize.query(
          `INSERT INTO "temp_seeder_data" ("key", "value") VALUES 
           ('demo_tenant_id', '${tenantId}'),
           ('admin_role_id', '${adminRoleId}'),
           ('manager_role_id', '${managerRoleId}'),
           ('user_role_id', '${userRoleId}')`
        );
      });
    });
  },

  async down(queryInterface, Sequelize) {
    // Get demo tenant ID
    const tenantData = await queryInterface.sequelize.query(
      'SELECT value FROM temp_seeder_data WHERE key = \'demo_tenant_id\'',
      { type: queryInterface.sequelize.QueryTypes.SELECT }
    );

    if (tenantData.length > 0) {
      const tenantId = tenantData[0].value;
      await queryInterface.bulkDelete('tenants', { id: tenantId });
    }

    // Clean up temp table
    await queryInterface.sequelize.query('DROP TABLE IF EXISTS "temp_seeder_data"');
  }
};
