'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {    const permissions = [
      // User Management
      { id: '550e8400-e29b-41d4-a716-446655440001', name: 'user.create', description: 'Create new users', resource: 'users', action: 'create', scope: 'tenant', is_system: true },
      { id: '550e8400-e29b-41d4-a716-446655440002', name: 'user.read', description: 'View user information', resource: 'users', action: 'read', scope: 'tenant', is_system: true },
      { id: '550e8400-e29b-41d4-a716-446655440003', name: 'user.update', description: 'Update user information', resource: 'users', action: 'update', scope: 'tenant', is_system: true },
      { id: '550e8400-e29b-41d4-a716-446655440004', name: 'user.delete', description: 'Delete users', resource: 'users', action: 'delete', scope: 'tenant', is_system: true },
      { id: '550e8400-e29b-41d4-a716-446655440005', name: 'user.assign_roles', description: 'Assign roles to users', resource: 'users', action: 'assign_roles', scope: 'tenant', is_system: true },

      // Role Management
      { id: '550e8400-e29b-41d4-a716-446655440010', name: 'role.create', description: 'Create new roles', resource: 'roles', action: 'create', scope: 'tenant', is_system: true },
      { id: '550e8400-e29b-41d4-a716-446655440011', name: 'role.read', description: 'View role information', resource: 'roles', action: 'read', scope: 'tenant', is_system: true },
      { id: '550e8400-e29b-41d4-a716-446655440012', name: 'role.update', description: 'Update role information', resource: 'roles', action: 'update', scope: 'tenant', is_system: true },
      { id: '550e8400-e29b-41d4-a716-446655440013', name: 'role.delete', description: 'Delete roles', resource: 'roles', action: 'delete', scope: 'tenant', is_system: true },
      { id: '550e8400-e29b-41d4-a716-446655440014', name: 'role.assign_permissions', description: 'Assign permissions to roles', resource: 'roles', action: 'assign_permissions', scope: 'tenant', is_system: true },

      // Customer Management
      { id: '550e8400-e29b-41d4-a716-446655440020', name: 'customer.create', description: 'Create new customers', resource: 'customers', action: 'create', scope: 'tenant', is_system: true },
      { id: '550e8400-e29b-41d4-a716-446655440021', name: 'customer.read', description: 'View customer information', resource: 'customers', action: 'read', scope: 'tenant', is_system: true },
      { id: '550e8400-e29b-41d4-a716-446655440022', name: 'customer.update', description: 'Update customer information', resource: 'customers', action: 'update', scope: 'tenant', is_system: true },
      { id: '550e8400-e29b-41d4-a716-446655440023', name: 'customer.delete', description: 'Delete customers', resource: 'customers', action: 'delete', scope: 'tenant', is_system: true },

      // Service Management
      { id: '550e8400-e29b-41d4-a716-446655440030', name: 'service.create', description: 'Create service calls', resource: 'services', action: 'create', scope: 'tenant', is_system: true },
      { id: '550e8400-e29b-41d4-a716-446655440031', name: 'service.read', description: 'View service calls', resource: 'services', action: 'read', scope: 'tenant', is_system: true },
      { id: '550e8400-e29b-41d4-a716-446655440032', name: 'service.update', description: 'Update service calls', resource: 'services', action: 'update', scope: 'tenant', is_system: true },
      { id: '550e8400-e29b-41d4-a716-446655440033', name: 'service.delete', description: 'Delete service calls', resource: 'services', action: 'delete', scope: 'tenant', is_system: true },

      // Sales Management
      { id: '550e8400-e29b-41d4-a716-446655440040', name: 'sale.create', description: 'Create sales records', resource: 'sales', action: 'create', scope: 'tenant', is_system: true },
      { id: '550e8400-e29b-41d4-a716-446655440041', name: 'sale.read', description: 'View sales records', resource: 'sales', action: 'read', scope: 'tenant', is_system: true },
      { id: '550e8400-e29b-41d4-a716-446655440042', name: 'sale.update', description: 'Update sales records', resource: 'sales', action: 'update', scope: 'tenant', is_system: true },
      { id: '550e8400-e29b-41d4-a716-446655440043', name: 'sale.delete', description: 'Delete sales records', resource: 'sales', action: 'delete', scope: 'tenant', is_system: true },

      // Reporting
      { id: '550e8400-e29b-41d4-a716-446655440050', name: 'report.view', description: 'View reports', resource: 'reports', action: 'view', scope: 'tenant', is_system: true },
      { id: '550e8400-e29b-41d4-a716-446655440051', name: 'report.export', description: 'Export reports', resource: 'reports', action: 'export', scope: 'tenant', is_system: true },

      // System Administration
      { id: '550e8400-e29b-41d4-a716-446655440060', name: 'system.admin', description: 'System administration access', resource: 'system', action: 'admin', scope: 'tenant', is_system: true },
      { id: '550e8400-e29b-41d4-a716-446655440061', name: 'tenant.manage', description: 'Manage tenant settings', resource: 'tenants', action: 'manage', scope: 'tenant', is_system: true },
    ];

    await queryInterface.bulkInsert('permissions', permissions.map(permission => ({
      ...permission,
      created_at: new Date(),
      updated_at: new Date(),
    })));
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete('permissions', null, {});
  }
};