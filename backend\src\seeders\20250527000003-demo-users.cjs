'use strict';

const bcrypt = require('bcryptjs');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const tenantId = '550e8400-e29b-41d4-a716-446655440100';
    
    // Hash passwords for demo users
    const hashedPassword = await bcrypt.hash('Demo@123', 12);    // Create demo users
    const users = [
      {
        id: '550e8400-e29b-41d4-a716-446655440300',
        tenant_id: tenantId,
        email: '<EMAIL>',
        password: hashedPassword,
        first_name: 'Super',
        last_name: 'Admin',
        phone: '+91-9876543210',
        is_active: true,
        is_verified: true,
        last_login_at: new Date(),
        preferences: JSON.stringify({
          theme: 'light',
          language: 'en',
          timezone: 'Asia/Kolkata'
        }),
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440301',
        tenant_id: tenantId,
        email: '<EMAIL>',
        password: hashedPassword,
        first_name: '<PERSON>',
        last_name: 'Manager',
        phone: '+91-9876543211',
        is_active: true,
        is_verified: true,
        last_login_at: new Date(),
        preferences: JSON.stringify({
          theme: 'light',
          language: 'en',
          timezone: 'Asia/Kolkata'
        }),
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440302',
        tenant_id: tenantId,
        email: '<EMAIL>',
        password: hashedPassword,
        first_name: 'Sarah',
        last_name: 'Sales',
        phone: '+91-9876543212',
        is_active: true,
        is_verified: true,
        last_login_at: new Date(),
        preferences: JSON.stringify({
          theme: 'light',
          language: 'en',
          timezone: 'Asia/Kolkata'
        }),
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440303',
        tenant_id: tenantId,
        email: '<EMAIL>',
        password: hashedPassword,
        first_name: 'Mike',
        last_name: 'Service',
        phone: '+91-9876543213',
        is_active: true,
        is_verified: true,
        last_login_at: new Date(),
        preferences: JSON.stringify({
          theme: 'light',
          language: 'en',
          timezone: 'Asia/Kolkata'
        }),
        created_at: new Date(),
        updated_at: new Date(),
      },
    ];

    await queryInterface.bulkInsert('users', users);

    // Assign roles to users
    const userRoles = [
      {
        user_id: '550e8400-e29b-41d4-a716-446655440300',
        role_id: '550e8400-e29b-41d4-a716-446655440200', // Super Admin
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        user_id: '550e8400-e29b-41d4-a716-446655440301',
        role_id: '550e8400-e29b-41d4-a716-446655440202', // Manager
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        user_id: '550e8400-e29b-41d4-a716-446655440302',
        role_id: '550e8400-e29b-41d4-a716-446655440203', // Sales Executive
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        user_id: '550e8400-e29b-41d4-a716-446655440303',
        role_id: '550e8400-e29b-41d4-a716-446655440204', // Service Engineer
        created_at: new Date(),
        updated_at: new Date(),
      },
    ];

    await queryInterface.bulkInsert('user_roles', userRoles);
  },

  async down(queryInterface, Sequelize) {
    // Remove user roles
    await queryInterface.bulkDelete('user_roles', {
      user_id: {
        [Sequelize.Op.in]: [
          '550e8400-e29b-41d4-a716-446655440300',
          '550e8400-e29b-41d4-a716-446655440301',
          '550e8400-e29b-41d4-a716-446655440302',
          '550e8400-e29b-41d4-a716-446655440303',
        ]
      }
    });

    // Remove users
    await queryInterface.bulkDelete('users', {
      tenant_id: '550e8400-e29b-41d4-a716-446655440100'
    });
  }
};
