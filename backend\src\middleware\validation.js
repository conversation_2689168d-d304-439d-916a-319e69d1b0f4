import { validationResult } from 'express-validator';
import { AppError } from './errorHandler.js';

/**
 * Middleware to validate request data using express-validator
 */
export const validateRequest = (req, res, next) => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    const errorMessages = errors.array().map(error => ({
      field: error.path || error.param,
      message: error.msg,
      value: error.value,
    }));
    
    const message = `Validation failed: ${errorMessages.map(e => e.message).join(', ')}`;
    
    return next(new AppError(message, 400, true, errorMessages));
  }
  
  next();
};

/**
 * Custom validation functions
 */
export const customValidators = {
  // Validate Indian phone number
  isIndianPhone: (value) => {
    const phoneRegex = /^[+]?91[-\s]?[6-9]\d{9}$/;
    return phoneRegex.test(value);
  },
  
  // Validate PAN number
  isPAN: (value) => {
    const panRegex = /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/;
    return panRegex.test(value);
  },
  
  // Validate GST number
  isGST: (value) => {
    const gstRegex = /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/;
    return gstRegex.test(value);
  },
  
  // Validate Aadhaar number
  isAadhaar: (value) => {
    const aadhaarRegex = /^[2-9]{1}[0-9]{3}[0-9]{4}[0-9]{4}$/;
    return aadhaarRegex.test(value);
  },
  
  // Validate strong password
  isStrongPassword: (value) => {
    // At least 8 characters, 1 uppercase, 1 lowercase, 1 number, 1 special character
    const strongPasswordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
    return strongPasswordRegex.test(value);
  },
  
  // Validate date format (YYYY-MM-DD)
  isValidDate: (value) => {
    const date = new Date(value);
    return date instanceof Date && !isNaN(date);
  },
  
  // Validate if date is in the future
  isFutureDate: (value) => {
    const date = new Date(value);
    const now = new Date();
    return date > now;
  },
  
  // Validate if date is in the past
  isPastDate: (value) => {
    const date = new Date(value);
    const now = new Date();
    return date < now;
  },
  
  // Validate Indian postal code
  isIndianPincode: (value) => {
    const pincodeRegex = /^[1-9][0-9]{5}$/;
    return pincodeRegex.test(value);
  },
  
  // Validate URL
  isValidURL: (value) => {
    try {
      new URL(value);
      return true;
    } catch {
      return false;
    }
  },
  
  // Validate file extension
  isValidFileExtension: (filename, allowedExtensions) => {
    const extension = filename.split('.').pop().toLowerCase();
    return allowedExtensions.includes(extension);
  },
  
  // Validate file size (in bytes)
  isValidFileSize: (size, maxSize) => {
    return size <= maxSize;
  },
  
  // Validate JSON string
  isValidJSON: (value) => {
    try {
      JSON.parse(value);
      return true;
    } catch {
      return false;
    }
  },
  
  // Validate array of IDs
  isArrayOfIds: (value) => {
    if (!Array.isArray(value)) return false;
    return value.every(id => Number.isInteger(Number(id)) && Number(id) > 0);
  },
  
  // Validate enum values
  isValidEnum: (value, enumValues) => {
    return enumValues.includes(value);
  },
  
  // Validate decimal number with specific precision
  isValidDecimal: (value, precision = 2) => {
    const regex = new RegExp(`^\\d+(\\.\\d{1,${precision}})?$`);
    return regex.test(value.toString());
  },
  
  // Validate Indian bank account number
  isBankAccount: (value) => {
    const bankAccountRegex = /^[0-9]{9,18}$/;
    return bankAccountRegex.test(value);
  },
  
  // Validate IFSC code
  isIFSC: (value) => {
    const ifscRegex = /^[A-Z]{4}0[A-Z0-9]{6}$/;
    return ifscRegex.test(value);
  },
};
